import { ProjectCreationProgress } from '@/stores/projectCreationStore';

export interface ProjectCreationEvent {
  type: 'setup' | 'progress' | 'step_complete' | 'complete' | 'error';
  data: ProjectCreationProgress | { totalSteps: number } | { stepIndex: number } | { projectId: string; name: string; description: string } | { error: string };
}

export class MockEventStream {
  private eventSource: EventTarget;
  private timeouts: NodeJS.Timeout[] = [];
  private isActive = false;

  constructor() {
    this.eventSource = new EventTarget();
  }

  // Mock progress steps that will be sent during project creation
  private getMockProgressSteps(ideaText: string): ProjectCreationProgress[] {
    return [
      {
        id: '1',
        message: 'Analyzing your idea...',
        completed: false,
        timestamp: Date.now(),
      },
      {
        id: '2',
        message: 'Generating project structure...',
        completed: false,
        timestamp: Date.now(),
      },
      {
        id: '3',
        message: 'Setting up development environment...',
        completed: false,
        timestamp: Date.now(),
      },
      {
        id: '4',
        message: 'Creating initial codebase...',
        completed: false,
        timestamp: Date.now(),
      },
      {
        id: '5',
        message: 'Configuring project settings...',
        completed: false,
        timestamp: Date.now(),
      },
      {
        id: '6',
        message: 'Finalizing project setup...',
        completed: false,
        timestamp: Date.now(),
      },
    ];
  }

  // Start the mock event stream
  startProjectCreation(ideaText: string): void {
    if (this.isActive) {
      this.stop();
    }

    this.isActive = true;
    const progressSteps = this.getMockProgressSteps(ideaText);

    // Send initial setup event with total steps
    this.dispatchEvent('setup', { totalSteps: progressSteps.length });

    // Send progress updates every 1.5 seconds
    progressSteps.forEach((step, index) => {
      const timeout = setTimeout(() => {
        if (!this.isActive) return;

        // Send progress start (current step)
        this.dispatchEvent('progress', step);

        // Mark as completed after 1 second
        const completeTimeout = setTimeout(() => {
          if (!this.isActive) return;

          const completedStep: ProjectCreationProgress = {
            ...step,
            completed: true,
            timestamp: Date.now(),
          };

          this.dispatchEvent('progress', completedStep);
          this.dispatchEvent('step_complete', { stepIndex: index });

          // If this is the last step, send completion after a short delay
          if (index === progressSteps.length - 1) {
            const finalTimeout = setTimeout(() => {
              if (!this.isActive) return;

              this.dispatchEvent('complete', {
                projectId: `proj_${Date.now()}`,
                name: this.generateProjectName(ideaText),
                description: ideaText,
              });
            }, 1000);

            this.timeouts.push(finalTimeout);
          }
        }, 1000);

        this.timeouts.push(completeTimeout);
      }, index * 1500);

      this.timeouts.push(timeout);
    });
  }

  // Generate a project name based on the idea text
  private generateProjectName(ideaText: string): string {
    const words = ideaText.toLowerCase().split(' ').filter(word => word.length > 2);
    const relevantWords = words.slice(0, 3);
    
    if (relevantWords.length === 0) {
      return 'New Project';
    }
    
    return relevantWords
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ') + ' Project';
  }

  // Add event listener
  addEventListener(type: string, listener: (event: CustomEvent<ProjectCreationEvent>) => void): void {
    this.eventSource.addEventListener(type, listener as EventListener);
  }

  // Remove event listener
  removeEventListener(type: string, listener: (event: CustomEvent<ProjectCreationEvent>) => void): void {
    this.eventSource.removeEventListener(type, listener as EventListener);
  }

  // Dispatch custom event
  private dispatchEvent(type: ProjectCreationEvent['type'], data: ProjectCreationEvent['data']): void {
    const event = new CustomEvent('message', {
      detail: { type, data }
    });
    this.eventSource.dispatchEvent(event);
  }

  // Stop the event stream
  stop(): void {
    this.isActive = false;
    this.timeouts.forEach(timeout => clearTimeout(timeout));
    this.timeouts = [];
  }

  // Simulate an error (for testing)
  simulateError(message: string = 'Something went wrong during project creation'): void {
    this.stop();
    this.dispatchEvent('error', { error: message });
  }
}

// Singleton instance for global use
export const mockEventStream = new MockEventStream();

// Hook for easier integration with React components
export const useProjectCreationStream = () => {
  const startCreation = (ideaText: string) => {
    mockEventStream.startProjectCreation(ideaText);
  };

  const stopCreation = () => {
    mockEventStream.stop();
  };

  const addEventListener = (listener: (event: CustomEvent<ProjectCreationEvent>) => void) => {
    mockEventStream.addEventListener('message', listener);
  };

  const removeEventListener = (listener: (event: CustomEvent<ProjectCreationEvent>) => void) => {
    mockEventStream.removeEventListener('message', listener);
  };

  return {
    startCreation,
    stopCreation,
    addEventListener,
    removeEventListener,
  };
};

// Alternative WebSocket-style API (you can switch to this if you prefer)
export class MockWebSocket {
  private mockStream: MockEventStream;
  public onmessage: ((event: { data: string }) => void) | null = null;
  public onopen: (() => void) | null = null;
  public onclose: (() => void) | null = null;
  public onerror: ((error: any) => void) | null = null;

  constructor(url: string) {
    this.mockStream = new MockEventStream();
    
    // Set up event forwarding
    this.mockStream.addEventListener('message', (event) => {
      if (this.onmessage) {
        this.onmessage({
          data: JSON.stringify(event.detail)
        });
      }
    });

    // Simulate connection opening
    setTimeout(() => {
      if (this.onopen) {
        this.onopen();
      }
    }, 100);
  }

  send(data: string): void {
    try {
      const message = JSON.parse(data);
      if (message.type === 'start_creation' && message.ideaText) {
        this.mockStream.startProjectCreation(message.ideaText);
      }
    } catch (error) {
      console.error('Invalid message format:', error);
    }
  }

  close(): void {
    this.mockStream.stop();
    if (this.onclose) {
      this.onclose();
    }
  }
}
