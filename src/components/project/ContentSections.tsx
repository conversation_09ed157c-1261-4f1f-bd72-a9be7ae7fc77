"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarButton } from "@/components/ui/sidebar-button";
import { Plus, X } from "lucide-react";

interface ContentSectionsProps {
  activeContent: "drafts" | "files" | null;
  setActiveContent: (content: "drafts" | "files" | null) => void;
  mockDraftItems: any[];
  mockFileItems: any[];
}

export function ContentSections({
  activeContent,
  setActiveContent,
  mockDraftItems,
  mockFileItems,
}: ContentSectionsProps) {
  if (activeContent === "drafts") {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <SidebarButton
              icon={X}
              variant="ghost"
              size="lg"
              layout="icon-only"
              showBorder={true}
              hoverColor="grey"
              borderClassName="border-1"
              hoverScale={true}
              onClick={() => setActiveContent(null)}
            />
            <h2 className="text-2xl font-bold text-gray-900">Drafts</h2>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-500">
              {mockDraftItems.length} drafts
            </div>
            <SidebarButton
              icon={Plus}
              variant="ghost"
              size="lg"
              showBorder={true}
              borderClassName="border-1"
              hoverColor="grey"
              hoverScale={true}
            >
              New Draft
            </SidebarButton>
          </div>
        </div>
        <div className="grid gap-4">
          {mockDraftItems.map((draft) => (
            <div
              key={draft.id}
              className="p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-all cursor-pointer"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900 text-lg">
                    {draft.title}
                  </h3>
                  <p className="text-sm text-gray-500 mt-1">
                    Modified {draft.lastModified}
                  </p>
                </div>
                <div className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                  {draft.status}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (activeContent === "files") {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setActiveContent(null)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
            <h2 className="text-2xl font-bold text-gray-900">Files</h2>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-500">
              {mockFileItems.length} files
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              New File
            </Button>
          </div>
        </div>
        <div className="grid gap-4">
          {mockFileItems.map((file) => (
            <div
              key={file.id}
              className="p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-all cursor-pointer"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                    <span className="text-gray-500 font-semibold text-sm">
                      {file.type === "image"
                        ? "🖼️"
                        : file.type === "design"
                        ? "🎨"
                        : "📄"}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 text-lg">
                      {file.title}
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      {file.size} • {file.type}
                    </p>
                  </div>
                </div>
                <div className="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                  {file.type}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return null;
}
