"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSidebar } from "@/components/ui/sidebar";
import { SidebarButton } from "@/components/ui/sidebar-button";
import { ICON_SIZES } from "@/lib/constants";
import { Eye, MoreVertical, Zap } from "lucide-react";
import { useState } from "react";

// Mock action items data for priorities
const mockActionItems = [
  { id: 1, title: "Research competitors", status: "pending", priority: "high" },
  { id: 2, title: "Create wireframes", status: "in-progress", priority: "high" },
  { id: 3, title: "Set up analytics", status: "completed", priority: "medium" },
  { id: 4, title: "Design logo", status: "pending", priority: "medium" },
  { id: 5, title: "Write content", status: "pending", priority: "low" },
];

export function PrioritiesDropdown() {
  const [isActionDropdownOpen, setIsActionDropdownOpen] = useState(false);
  const { isMobile } = useSidebar();

  return (
    <DropdownMenu open={isActionDropdownOpen} onOpenChange={setIsActionDropdownOpen}>
      <DropdownMenuTrigger asChild>
        <SidebarButton
          text={isMobile ? "P" : "Priorities"}
          badge={mockActionItems.length}
          variant="ghost"
          layout="horizontal"
          hoverColor="green"
          hoverScale={true}
          showBorder={true}
          className="bg-green-100 hover:bg-green-600 text-green-700 hover:text-white border-green-500 border-2"
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-80 rounded-lg animate-in fade-in-0 zoom-in-95"
        align="end"
        side="bottom"
        sideOffset={4}
      >
        <DropdownMenuLabel className="text-muted-foreground text-xs">
          Project Actions ({mockActionItems.length})
        </DropdownMenuLabel>
        {mockActionItems.map((action) => (
          <DropdownMenuItem
            key={action.id}
            className="gap-2 p-3 m-1 border border-transparent transition-colors duration-200 hover:bg-gray-100 hover:border-gray-200 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:border-gray-700 dark:hover:text-gray-100 cursor-pointer rounded-md"
          >
            <div className="flex size-8 items-center justify-center rounded-md border border-yellow-500 bg-yellow-50">
              <Zap className={`${ICON_SIZES.md} shrink-0 text-yellow-500`} />
            </div>
            <div className="flex flex-col flex-1">
              <span className="font-medium">{action.priority.toUpperCase()}</span>
              <span className="text-xs text-muted-foreground capitalize">
                {action.title}
              </span>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded transition-colors duration-200 flex items-center justify-center">
                  <MoreVertical className={ICON_SIZES.md} />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                <DropdownMenuItem className="gap-2 cursor-pointer hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100">
                  <Eye className={ICON_SIZES.md} />
                  View
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
