"use client";

import { Logo } from "@/components/ui/logo";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { motion, AnimatePresence } from "framer-motion";

import { useEffect } from "react";

export function ProjectCreationAnimation() {
  const {
    currentStep,
    currentProgress,
    totalSteps,
    completedSteps,
    showLogo,
    logoRotating,
    showGlow,
  } = useProjectCreationStore();

  // Lock body scroll during project creation
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  // Handle project completion - stop logo animation
  useEffect(() => {
    if (currentStep === 'completed') {
      console.log('Project completed, stopping logo animation...');
      // Stop logo rotation when project is completed
      const { setLogoRotating } = useProjectCreationStore.getState();
      setLogoRotating(false);
    }
  }, [currentStep]);

  if (currentStep === 'idle') {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 bg-background overflow-hidden">

      {/* Noise Texture Overlay */}
      <div
        className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          backgroundSize: "256px 256px",
        }}
      />

      {/* Main Content */}
      <div className="relative z-30 flex flex-col items-center justify-center min-h-screen p-8">
        {/* Logo with Glow Effect */}
        <AnimatePresence>
          {showLogo && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.5 }}
              className="relative mb-12"
            >
              {/* Glow Effect */}
              {showGlow && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="absolute inset-0 -m-8"
                >
                  <div className="absolute inset-0 bg-[#166534]/20 rounded-full blur-3xl animate-pulse" />
                  <div className="absolute inset-0 bg-[#22c55e]/10 rounded-full blur-2xl animate-pulse" />
                </motion.div>
              )}

              {/* Logo */}
              <div className="relative z-10">
                <Logo size={120} animated={logoRotating} />
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {currentStep === 'completed' ? (
          /* Completion Message */
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center space-y-4"
          >
            <p className="text-lg font-semibold text-[#166534]">
              Project is created
            </p>
            <motion.button
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.3 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-[#166534] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#166534]/90 transition-all duration-200"
              onClick={() => {
                window.location.href = '/user-dashboard';
              }}
            >
              Go to Dashboard
            </motion.button>
          </motion.div>
        ) : (
          /* Progress Section */
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="w-full max-w-md space-y-4"
          >
            <h2 className="text-2xl font-bold text-center text-foreground mb-8">
              Creating Your Project
            </h2>

            {/* Current Progress Item */}
            <div className="space-y-3">
              <AnimatePresence mode="wait">
                {currentProgress ? (
                  <motion.div
                    key={currentProgress.id}
                    initial={{ opacity: 0, x: -20, scale: 0.95 }}
                    animate={{ opacity: 1, x: 0, scale: 1 }}
                    exit={{ opacity: 0, x: 20, scale: 0.95 }}
                    transition={{ duration: 0.4, ease: "easeOut" }}
                    className={`flex items-center gap-4 p-6 rounded-xl backdrop-blur border-2 transition-all duration-300 ${
                      currentProgress.completed
                        ? "bg-[#166534]/10 border-[#166534]/30 shadow-lg shadow-[#166534]/20"
                        : "bg-card/50 border-border"
                    }`}
                  >
                    <div className="flex-shrink-0">
                      {currentProgress.completed ? (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{
                            type: "spring",
                            stiffness: 500,
                            damping: 30,
                            delay: 0.1
                          }}
                          className="w-7 h-7 bg-[#166534] rounded-full flex items-center justify-center"
                        >
                          <svg
                            className="w-4 h-4 text-white"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </motion.div>
                      ) : (
                        <div className="w-7 h-7 border-2 border-muted-foreground rounded-full animate-pulse" />
                      )}
                    </div>
                    <span
                      className={`text-lg font-medium transition-colors duration-300 ${
                        currentProgress.completed
                          ? "text-[#166534] font-semibold"
                          : "text-foreground"
                      }`}
                    >
                      {currentProgress.message}
                    </span>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex items-center justify-center p-6 rounded-xl bg-card/30 backdrop-blur border border-dashed"
                  >
                    <span className="text-muted-foreground">Initializing project creation...</span>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Progress Bar */}
            <div className="mt-6">
              <div className="flex justify-between text-sm text-muted-foreground mb-2">
                <span>Progress</span>
                <span>
                  {completedSteps} / {totalSteps}
                </span>
              </div>
              <div className="w-full bg-[var(--progress-bg)] border border-[var(--progress-border)] rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{
                    width: `${
                      totalSteps > 0
                        ? (completedSteps / totalSteps) * 100
                        : 0
                    }%`,
                  }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                  className="bg-[var(--progress-fill)] h-2 rounded-full"
                />
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
