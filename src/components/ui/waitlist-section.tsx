"use client";
import { motion } from "motion/react";
import React from "react";
import { BackgroundBeams } from "./background-beams";
import { Button } from "./button";
import { Input } from "./input";

export const WaitlistSection = () => {
  const [email, setEmail] = React.useState("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Here you would typically send the email to your backend
    console.log("Email submitted:", email);

    setEmail("");
    setIsSubmitting(false);
  };

  return (
    <div className="h-[40rem] w-full rounded-md bg-background relative flex flex-col items-center justify-center antialiased overflow-hidden">
      <div className="max-w-2xl mx-auto p-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <h1 className="relative z-10 text-lg md:text-5xl bg-clip-text text-transparent bg-gradient-to-b from-foreground to-muted-foreground text-center font-sans font-bold mb-6">
          ready for a reality check?
          </h1>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <p className="text-muted-foreground max-w-lg mx-auto my-2 text-md text-center relative z-10">
          de-risk your business moves with ease

          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.25, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="mt-4 mb-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto text-xs text-muted-foreground relative z-10">
            <div className="flex items-center justify-center gap-2">
              <span className="text-green-500">✓</span>
              <span>Automatic founder best practices</span>
            </div>
            <div className="flex items-center justify-center gap-2">
              <span className="text-green-500">✓</span>
              <span>Filter blindspots & distractions</span>
            </div>
            <div className="flex items-center justify-center gap-2">
              <span className="text-green-500">✓</span>
              <span>Organize thoughts on auto-pilot</span>
            </div>
            <div className="flex items-center justify-center gap-2">
              <span className="text-green-500">✓</span>
              <span>100% private & secure</span>
            </div>
          </div>
        </motion.div>

        <motion.form
          onSubmit={handleSubmit}
          className="relative mt-6 w-full"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
        >
          <Input
            type="email"
            placeholder="Enter your email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full pr-32 relative z-10 py-6"
            required
          />
          <Button
            type="submit"
            disabled={isSubmitting || !email}
            className="absolute right-2 top-1/2 -translate-y-1/2 z-20"
            size="sm"
          >
            {isSubmitting ? "Joining..." : "Join Waitlist"}
          </Button>
        </motion.form>

        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="mt-4 text-center"
        >
          <p className="text-xs text-muted-foreground relative z-10">
            Join 500+ entrepreneurs already on the waitlist
          </p>
        </motion.div>
      </div>
      <BackgroundBeams />
    </div>
  );
};
