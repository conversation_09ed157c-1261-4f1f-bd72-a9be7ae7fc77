"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, ArrowRight, MoreHorizontal } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

// Updated: Removed add new project button, removed progress, reduced padding, added hover effects

export function DashboardProjectsSection() {
  const router = useRouter();

  // Mock project data - in real app this would come from API
  const projects = [
    {
      id: "1",
      name: "Website Redesign",
      description: "Complete redesign of company website with modern UI",
      status: "active",
      progress: 75,
      color: "blue",
      initials: "WR"
    },
    {
      id: "2", 
      name: "Mobile App",
      description: "iOS and Android app development for the platform",
      status: "in-progress",
      progress: 45,
      color: "purple",
      initials: "MA"
    },
    {
      id: "3",
      name: "Marketing Campaign",
      description: "Q1 marketing campaign planning and execution",
      status: "completed",
      progress: 100,
      color: "green",
      initials: "MC"
    },
    {
      id: "4",
      name: "API Documentation",
      description: "Comprehensive API documentation and developer guides",
      status: "active",
      progress: 85,
      color: "indigo",
      initials: "AD"
    },
    {
      id: "5",
      name: "Database Migration",
      description: "Migrate legacy database to new cloud infrastructure",
      status: "planning",
      progress: 15,
      color: "red",
      initials: "DM"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "in-progress":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
      case "completed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
      case "planning":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  const getProgressColor = (color: string) => {
    switch (color) {
      case "blue":
        return "bg-blue-600";
      case "purple":
        return "bg-purple-600";
      case "green":
        return "bg-green-600";
      case "indigo":
        return "bg-indigo-600";
      case "red":
        return "bg-red-600";
      default:
        return "bg-gray-600";
    }
  };

  const getAvatarColor = (color: string) => {
    switch (color) {
      case "blue":
        return "bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400";
      case "purple":
        return "bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400";
      case "green":
        return "bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400";
      case "indigo":
        return "bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400";
      case "red":
        return "bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400";
      default:
        return "bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400";
    }
  };

  const handleViewAllProjects = () => {
    router.push("/projects");
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gray-50 dark:bg-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                Recent Projects
              </CardTitle>
              <CardDescription>
                Your active projects and quick access to create new ones.
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleViewAllProjects}
              className="border-2 hover:bg-primary/10 hover:text-primary transition-all duration-200"
            >
              <ArrowRight className="mr-2 h-4 w-4" />
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* Project Cards */}
            {projects.slice(0, 6).map((project) => (
              <Card
                key={project.id}
                className="bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group"
                onClick={() => router.push(`/projects/${project.id}`)}
              >
                <CardContent className="px-4 py-0">
                  <div className="flex items-start justify-between mb-3">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getAvatarColor(project.color)}`}>
                      <span className="font-semibold">
                        {project.initials}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className={getStatusColor(project.status)}>
                        {project.status.replace("-", " ")}
                      </Badge>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <h3 className="font-medium mb-2">{project.name}</h3>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {project.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
