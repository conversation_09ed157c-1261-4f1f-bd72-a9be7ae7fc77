"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>Content,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { MessageCircle, Columns2, Columns3 } from "lucide-react";
import { useState } from "react";
import { useSidebar } from "@/components/ui/sidebar";
import {
  ChatBubble,
  ChatBubbleMessage,
} from "./ui/chat-bubble";
import { ChatMessageList } from "./ui/chat-message-list";
import { Logo } from "./ui/logo";
import { ICON_SIZES } from "@/lib/constants";
import { AI_Prompt } from "./ui/animated-ai-input";

interface ProjectChatSidebarProps {
  projectId: string;
  isCollapsed?: boolean;
  onCollapseChange?: (collapsed: boolean) => void;
  embedded?: boolean; // New prop to indicate if it's embedded in sidebar
  chatWidth?: '45%' | '45%';
  setChatWidth?: (width: '45%' | '45%') => void;
  isChatCollapsed?: boolean;
  setIsChatCollapsed?: (collapsed: boolean) => void;
  selectedBusinessItem?: any;
}

// Mock chat data
const mockMessages = [
  {
    id: 1,
    user: "Siift AI",
    avatar: "",
    message: "Welcome to your project! I'm here to help you manage tasks, answer questions, and provide insights.",
    timestamp: "9:00 AM",
    isCurrentUser: false,
  },
  {
    id: 2,
    user: "You",
    avatar: "",
    message: "Hey! I've finished the homepage design. Can you review it?",
    timestamp: "10:30 AM",
    isCurrentUser: true,
  },
  {
    id: 3,
    user: "Siift AI",
    avatar: "",
    message: "Looks great! The design follows modern UI principles. I can help you implement the responsive layout if needed.",
    timestamp: "10:32 AM",
    isCurrentUser: false,
  },
  {
    id: 4,
    user: "You",
    avatar: "",
    message: "Perfect! I'll work on the component library in parallel.",
    timestamp: "10:35 AM",
    isCurrentUser: true,
  },
  {
    id: 5,
    user: "Siift AI",
    avatar: "",
    message: "I notice you're making great progress on the responsive layout. Would you like me to suggest some best practices for mobile optimization?",
    timestamp: "10:45 AM",
    isCurrentUser: false,
  },
  {
    id: 6,
    user: "You",
    avatar: "",
    message: "Great progress everyone! Let's sync up tomorrow morning.",
    timestamp: "11:15 AM",
    isCurrentUser: true,
  },
  {
    id: 7,
    user: "Siift AI",
    avatar: "",
    message: "Sounds good! I should have the navigation component ready by then.",
    timestamp: "11:18 AM",
    isCurrentUser: false,
  },
  {
    id: 8,
    user: "You",
    avatar: "",
    message: "I'll prepare the test cases for the new features.",
    timestamp: "11:45 AM",
    isCurrentUser: true,
  },
];

export function ProjectChatSidebar({
  projectId,
  isCollapsed: externalIsCollapsed,
  onCollapseChange,
  embedded = false,
  chatWidth: externalChatWidth,
  setChatWidth: externalSetChatWidth,
  isChatCollapsed: externalIsChatCollapsed,
  setIsChatCollapsed: externalSetIsChatCollapsed,
  selectedBusinessItem,
}: ProjectChatSidebarProps) {
  const [messages, setMessages] = useState(mockMessages);
  const [internalIsCollapsed, setInternalIsCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [internalChatWidth, setInternalChatWidth] = useState<'45%' | '45%'>('45%');
  const { setOpen, state } = useSidebar();

  // Use external state if provided, otherwise use internal state
  const isCollapsed =
    externalIsCollapsed !== undefined
      ? externalIsCollapsed
      : internalIsCollapsed;
  const setIsCollapsed = onCollapseChange || setInternalIsCollapsed;

  // Chat is always expanded - no collapse functionality
  const isChatCollapsed = false;
  const setIsChatCollapsed = () => {}; // No-op function

  const chatWidth =
    externalChatWidth !== undefined
      ? externalChatWidth
      : internalChatWidth;
  const setChatWidth = externalSetChatWidth || setInternalChatWidth;



  const handleWidthToggle = () => {
    if (setChatWidth) {
      setChatWidth(chatWidth === '45%' ? '45%' : '45%');
    }
  };

  // Render embedded version for sidebar
  if (embedded) {
    // Don't show anything when sidebar is collapsed
    if (state === "collapsed") {
      return null;
    }

    // Always show expanded chat - no collapse functionality
    return (
      <div 
        className={`w-full h-full border-t border-border ${
          selectedBusinessItem ? 'bg-[#E8F5E9]/50 dark:bg-green-950/20' : 'bg-[#F5F5F5] dark:bg-yellow-950/20'
        } backdrop-blur-sm flex flex-col transition-all duration-300`}
      >
        {/* Header */}
     

        {/* Messages */}
        <div className="flex-1 overflow-hidden w-full min-h-0 relative">
          
          <ChatMessageList className="w-full h-full">
            {messages.map((msg) => (
              <ChatBubble
                key={msg.id}
                className="mt-2"
                variant={msg.isCurrentUser ? "sent" : "received"}
              >
                {!msg.isCurrentUser && (
                  <div className="h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Logo size={20} />
                  </div>
                )}
                <ChatBubbleMessage
                  variant={msg.isCurrentUser ? "sent" : "received"}>
                  <div className="text-sm leading-relaxed whitespace-pre-line">{msg.message}</div>
                </ChatBubbleMessage>
              </ChatBubble>
            ))}
          </ChatMessageList>
        </div>

        {/* Message Input */}
        <div className="p-3 border-t border-border/50 bg-muted/30 backdrop-blur-sm w-full flex-shrink-0">
          <AI_Prompt />
          
          {/* Disclaimer */}
          <div className="mt-1 px-2">
            <p className="text-[10px] text-center opacity-50 leading-tight">
              Siift can make mistakes. Please double check answers to ensure accuracy.
            </p>
          </div>
        </div>
      </div>
    );
  }
 
}
