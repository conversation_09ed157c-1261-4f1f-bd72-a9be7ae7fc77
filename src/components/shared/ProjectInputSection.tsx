"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useAnalytics } from "@/hooks/useAnalytics";
import { useClerkAuth } from "@/hooks/useClerkAuth";
import { useProjectCreationStream } from "@/lib/mockEventStream";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { ArrowRight, Sparkles, Target, Users, Zap } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface ProjectInputSectionProps {
  variant: "dashboard" | "landing";
  title?: string;
  subtitle?: string;
  showWelcomeBadge?: boolean;
  showQuickActions?: boolean;
  showFeatureBadges?: boolean;
}

export function ProjectInputSection({
  variant,
  title,
  subtitle,
  showWelcomeBadge = false,
  showQuickActions = false,
  showFeatureBadges = false,
}: ProjectInputSectionProps) {
  const { isSignedIn } = useClerkAuth();
  const { trackClick, trackCustomEvent } = useAnalytics();
  const router = useRouter();
  
  const [currentHintIndex, setCurrentHintIndex] = useState(0);
  const [displayedText, setDisplayedText] = useState("");
  const [isTyping, setIsTyping] = useState(true);
  const [projectInput, setProjectInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Project creation store
  const {
    setIdeaText,
    startCreation,
    setCurrentProgress,
    setTotalSteps,
    incrementCompletedSteps,
    setCurrentStep,
    setCreatedProject,
  } = useProjectCreationStore();

  // Event stream hook
  const {
    startCreation: startStream,
    addEventListener,
    removeEventListener,
  } = useProjectCreationStream();

  const hints = [
    "share your business idea & I'll help bring it into reality",
    "Describe your next project idea",
    "What do you want to build?",
    "What's on your mind?",
  ];

  // Typing animation effect
  useEffect(() => {
    const currentHint = hints[currentHintIndex];
    let timeoutId: NodeJS.Timeout;

    if (isTyping) {
      if (displayedText.length < currentHint.length) {
        timeoutId = setTimeout(() => {
          setDisplayedText(currentHint.slice(0, displayedText.length + 1));
        }, 50);
      } else {
        timeoutId = setTimeout(() => {
          setIsTyping(false);
        }, 500);
      }
    } else {
      if (displayedText.length > 0) {
        timeoutId = setTimeout(() => {
          setDisplayedText(displayedText.slice(0, -1));
        }, 25);
      } else {
        setCurrentHintIndex((prev) => (prev + 1) % hints.length);
        setIsTyping(true);
      }
    }

    return () => clearTimeout(timeoutId);
  }, [displayedText, isTyping, currentHintIndex, hints]);

  // Event stream listener for dashboard variant
  useEffect(() => {
    if (variant !== "dashboard") return;

    const handleStreamEvent = (event: CustomEvent) => {
      const { type, data } = event.detail;

      switch (type) {
        case "setup":
          setTotalSteps(data.totalSteps);
          break;
        case "progress":
          setCurrentProgress(data);
          break;
        case "step_complete":
          incrementCompletedSteps();
          break;
        case "complete":
          setCreatedProject({
            id: data.projectId,
            name: data.name,
            description: data.description,
          });
          setCurrentStep("completed");
          setIsSubmitting(false);
          break;
        case "error":
          console.error("Project creation error:", data.error);
          setIsSubmitting(false);
          break;
      }
    };

    addEventListener(handleStreamEvent);
    return () => removeEventListener(handleStreamEvent);
  }, [
    variant,
    addEventListener,
    removeEventListener,
    setCurrentProgress,
    setTotalSteps,
    incrementCompletedSteps,
    setCreatedProject,
    setCurrentStep,
  ]);

  const handleSubmit = () => {
    if (!projectInput.trim()) return;

    if (variant === "dashboard") {
      // Dashboard: Direct project creation
      setIsSubmitting(true);
      setIdeaText(projectInput);
      startCreation();
      startStream(projectInput);
    } else {
      // Landing: Analytics and routing
      trackClick("generate-project-button", "hero-section");
      trackCustomEvent("project_generation_attempted", {
        user_signed_in: isSignedIn,
        project_input_length: projectInput.length,
        has_project_input: projectInput.trim().length > 0,
        redirect_destination: isSignedIn ? "dashboard" : "register",
      });

      if (isSignedIn && projectInput.trim()) {
        setIdeaText(projectInput);
        startCreation();
        startStream(projectInput);
        router.push("/user-dashboard");
      } else if (isSignedIn) {
        router.push("/user-dashboard");
      } else {
        router.push("/auth/register");
      }
    }
  };

  const setQuickAction = (text: string, type: string) => {
    if (variant === "landing") {
      trackClick("quick-action-badge", "hero-section");
      trackCustomEvent("badge_clicked", {
        badge_type: type,
        location: "hero-section",
      });
    }
    setProjectInput(text);
  };

  // Theme-based styling
  const isDashboard = variant === "dashboard";
  const cardBg = isDashboard 
    ? "bg-[#166534]/5 hover:bg-[#166534]/8" 
    : "bg-card/80";
  const gradientOverlay = isDashboard
    ? "from-[#166534]/8 via-transparent to-[#22c55e]/8"
    : "from-[var(--primary)]/5 via-transparent to-[var(--primary-light)]/5";
  const inputBg = isDashboard
    ? "bg-[#166534]/5 hover:bg-[#166534]/10"
    : "bg-background/50";
  const inputFocus = isDashboard
    ? "focus:ring-[#26F000]/40"
    : "focus:ring-[var(--primary)]/20 focus:border-[var(--primary)]";
  const buttonBg = isDashboard
    ? "bg-[#CEFFC5] hover:bg-[#26F000] text-black hover:text-black"
    : "bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)]";

  return (
    <div className="w-full max-w-lg mx-auto">
      {/* Header Content */}
      {(title || subtitle || showWelcomeBadge) && (
        <div className="mb-8 text-center">
          {showWelcomeBadge && (
            <div className="flex items-center justify-center gap-2 mb-4">
              <Badge
                variant="secondary"
                className="bg-[#166534]/10 text-[#166534] border-[#166534]/20"
              >
                <Sparkles className="w-3 h-3 mr-1" />
                Welcome back
              </Badge>
            </div>
          )}
          {title && (
            <h1 className="text-xl md:text-2xl lg:text-4xl font-regular text-foreground mb-6 leading-tight">
              {title}
            </h1>
          )}
          {subtitle && (
            <p className="text-lg md:text-xl leading-relaxed tracking-tight text-muted-foreground max-w-2xl mx-auto">
              {subtitle}
            </p>
          )}
        </div>
      )}

      {/* Input Card */}
      <div className={`group relative ${cardBg} backdrop-blur border rounded-2xl p-6 space-y-4 shadow-xl overflow-hidden transition-all duration-300`}>
        {/* Gradient overlay */}
        <div className={`absolute inset-0 bg-gradient-to-br ${gradientOverlay} pointer-events-none`} />
        
        <div className="relative z-10">
          {/* Target label for landing variant */}
          {variant === "landing" && (
            <div className="flex items-center gap-2 text-muted-foreground mb-4">
              <Target className="w-4 h-4" />
              <span className="text-sm">Ask Siift to create a project that...</span>
            </div>
          )}

          {/* Input area */}
          <div className="relative">
            <textarea
              value={projectInput}
              onChange={(e) => {
                setProjectInput(e.target.value);
                if (variant === "landing" && e.target.value.length === 1) {
                  trackCustomEvent("project_input_started", {
                    location: "hero-section",
                  });
                }
              }}
              onFocus={() => {
                if (variant === "landing") {
                  trackCustomEvent("project_input_focused", {
                    location: "hero-section",
                  });
                }
              }}
              placeholder={displayedText}
              className={`w-full px-4 py-3 pr-16 rounded-lg ${inputBg} backdrop-blur text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 ${inputFocus} resize-none min-h-[80px] transition-all duration-300 hover:shadow-lg relative z-10`}
              rows={3}
              disabled={isSubmitting}
            />
            <Button
              onClick={handleSubmit}
              disabled={!projectInput.trim() || isSubmitting}
              className={`absolute right-3 bottom-4 ${buttonBg} shadow-lg z-20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300`}
              size="icon"
            >
              {isSubmitting ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <ArrowRight className="w-4 h-4" />
              )}
            </Button>
          </div>

          {/* Feature badges for dashboard */}
          {showFeatureBadges && (
            <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground mt-4">
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                AI-powered insights
              </span>
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                Smart project planning
              </span>
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
                Team collaboration
              </span>
            </div>
          )}

          {/* Quick action badges for landing */}
          {showQuickActions && (
            <div className="flex flex-wrap gap-2 justify-center mt-4">
              <Badge
                variant="outline"
                className="text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer"
                onClick={() => setQuickAction(
                  "Create a project tracker that manages team tasks and deadlines",
                  "project_tracker"
                )}
              >
                <Zap className="w-3 h-3 mr-1" />
                Project tracker
              </Badge>
              <Badge
                variant="outline"
                className="text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer"
                onClick={() => setQuickAction(
                  "Create a team collaboration platform for better communication",
                  "team_collaboration"
                )}
              >
                <Users className="w-3 h-3 mr-1" />
                Team collaboration
              </Badge>
              <Badge
                variant="outline"
                className="text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer"
                onClick={() => setQuickAction(
                  "Create a goal management system to track objectives and milestones",
                  "goal_management"
                )}
              >
                <Target className="w-3 h-3 mr-1" />
                Goal management
              </Badge>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
