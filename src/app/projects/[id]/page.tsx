"use client";

import { <PERSON><PERSON><PERSON>er, ProjectMainContent } from "@/components/project";
import { ProjectSidebar } from "@/components/project-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useResizable } from "../../../hooks/useResizable";
import { fetchBusinessSections } from "../../../lib/businessSectionsData";
import { useBusinessItemStore } from "../../../stores/businessItemStore";
import { useBusinessSectionStore } from "../../../stores/businessSectionStore";

// Mock data
const mockDraftItems = [
  {
    id: 1,
    title: "Project proposal",
    status: "draft",
    lastModified: "2 hours ago",
  },
  { id: 2, title: "Design brief", status: "draft", lastModified: "1 day ago" },
  {
    id: 3,
    title: "Technical specs",
    status: "draft",
    lastModified: "3 days ago",
  },
];

// Mock file items data
const mockFileItems = [
  { id: 1, title: "logo.svg", type: "image", size: "24KB" },
  { id: 2, title: "wireframes.fig", type: "design", size: "1.2MB" },
  { id: 3, title: "requirements.pdf", type: "document", size: "156KB" },
  { id: 4, title: "styleguide.pdf", type: "document", size: "2.1MB" },
];

export default function ProjectDetailPage() {
  const params = useParams();
  const projectId = params.id;
  const [activeContent, setActiveContent] = useState<"drafts" | "files" | null>(
    null
  );
  const [chatWidth, setChatWidth] = useState<"45%" | "45%">("45%");
  const [isChatCollapsed, setIsChatCollapsed] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState("45vw");

  // Sync sidebar width with chat width
  const handleChatWidthChange = (width: "45%" | "45%") => {
    setChatWidth(width);
    setSidebarWidth(width === "45%" ? "45vw" : "45vw");
  };

  const { sections, isLoading, error, setSections, setLoading, setError } =
    useBusinessSectionStore();
  const { selectedItem, itemDetails, setSelectedItem, setItemDetails } =
    useBusinessItemStore();

  // Dynamic sidebar width based on selected item
  const currentSidebarWidth = selectedItem ? "30vw" : sidebarWidth;

  // Reset to default view on page load/refresh
  useEffect(() => {
    setSelectedItem(null);
    setItemDetails([]);
  }, [setSelectedItem, setItemDetails]);

  // Load business sections on mount
  useEffect(() => {
    const loadBusinessSections = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await fetchBusinessSections(projectId as string);
        setSections(data);
      } catch (err) {
        setError("Failed to load business sections");
        console.error("Error loading business sections:", err);
      } finally {
        setLoading(false);
      }
    };

    loadBusinessSections();
  }, [setSections, setLoading, setError]);

  // Handle business item selection - show detail view
  const handleBusinessItemClick = (item: any) => {
    setSelectedItem(item);

    // Load mock item details for the selected business item
    const loadItemDetails = async () => {
      try {
        const { fetchItemDetails } = await import("@/stores/businessItemStore");
        const details = await fetchItemDetails(item.id);
        setItemDetails(details);
      } catch (err) {
        console.error("Error loading item details:", err);
        setItemDetails([]);
      }
    };

    loadItemDetails();
  };

  // Handle back to items
  const handleBackToItems = () => {
    setSelectedItem(null);
  };

  // Resize functionality
  const resizable = useResizable({
    initialWidth: sidebarWidth,
    minWidthPercent: 10,
    maxWidthPercent: 70,
    onWidthChange: (width) => {
      // Convert percentage to viewport width
      const widthPercent = parseFloat(width.replace("%", ""));
      const vwWidth = `${widthPercent}vw`;
      setSidebarWidth(vwWidth);

      // Update chatWidth to match sidebarWidth for consistency
      if (widthPercent <= 70) {
        setChatWidth("45%");
      } else {
        setChatWidth("45%");
      }
    },
    onCollapse: () => {
      setIsChatCollapsed(true);
    },
  });

  return (
    <div className="h-screen overflow-hidden">
      <SidebarProvider
        defaultOpen={true}
        style={
          {
            "--sidebar-width": currentSidebarWidth,
            "--sidebar-width-mobile": "18rem",
            "--sidebar-width-icon": "5rem",
            transition: "all 0.3s ease-in-out",
          } as React.CSSProperties
        }
      >
        <ProjectSidebar
          projectId={projectId as string}
          chatWidth={chatWidth}
          setChatWidth={handleChatWidthChange}
          isChatCollapsed={isChatCollapsed}
          setIsChatCollapsed={setIsChatCollapsed}
          selectedBusinessItem={selectedItem}
          showDescription={!!selectedItem}
          onBackToProject={handleBackToItems}
          resizeHandle={{
            onMouseDown: resizable.handleMouseDown,
            isDragging: resizable.isDragging,
          }}
        />
        <SidebarInset className="flex-1 flex flex-col h-screen overflow-hidden">
          <ProjectHeader
            activeContent={activeContent}
            setActiveContent={setActiveContent}
            selectedBusinessItem={selectedItem}
            onBackToItems={handleBackToItems}
          />

          <ProjectMainContent
            activeContent={activeContent}
            setActiveContent={setActiveContent}
            mockDraftItems={mockDraftItems}
            mockFileItems={mockFileItems}
            selectedItem={selectedItem}
            itemDetails={itemDetails}
            sections={sections}
            isLoading={isLoading}
            error={error}
            onBusinessItemClick={handleBusinessItemClick}
            onBackToItems={handleBackToItems}
          />

          {/* Bottom fade effect to indicate more content - fixed at bottom of viewport */}
          <div
            className="fixed bottom-0 h-16 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 dark:to-transparent pointer-events-none z-10"
            style={{
              left: `var(--sidebar-width, 45vw)`,
              right: "0",
            }}
          />
          <div
            className="fixed bottom-0 h-8 backdrop-blur-sm pointer-events-none z-10"
            style={{
              left: `var(--sidebar-width, 45vw)`,
              right: "0",
            }}
          />
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}
