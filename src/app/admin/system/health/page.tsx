"use client";

// Prevent static generation for this page
export const dynamic = 'force-dynamic';

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/layout/admin-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import {
  Loader2,
  RefreshCw,
  Shield,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Server,
  Database,
  Activity,
  AlertCircle,
} from "lucide-react";

export default function SystemHealthPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const token =
        localStorage.getItem("siift_access_token") ||
        sessionStorage.getItem("siift_access_token");

      if (!token) {
        throw new Error("No admin token found. Please login first.");
      }

      initializeAdminApi(token);

      const result = await adminApi.getHealthCheck();
      setData(result);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "healthy":
      case "ok":
      case "operational":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "warning":
      case "degraded":
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case "error":
      case "down":
      case "critical":
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Activity className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "healthy":
      case "ok":
      case "operational":
        return "bg-green-100 text-green-800";
      case "warning":
      case "degraded":
        return "bg-yellow-100 text-yellow-800";
      case "error":
      case "down":
      case "critical":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getOverallStatus = () => {
    if (!data) return "unknown";
    
    const systemStatus = data.status?.toLowerCase();
    const services = data.services || {};
    
    // Check if any service is down
    const hasErrors = Object.values(services).some((status: any) => 
      status?.toLowerCase() === "error" || status?.toLowerCase() === "down"
    );
    
    // Check if any service has warnings
    const hasWarnings = Object.values(services).some((status: any) => 
      status?.toLowerCase() === "warning" || status?.toLowerCase() === "degraded"
    );
    
    if (systemStatus === "error" || hasErrors) return "error";
    if (systemStatus === "warning" || hasWarnings) return "warning";
    if (systemStatus === "healthy" || systemStatus === "ok") return "healthy";
    
    return "unknown";
  };

  const overallStatus = getOverallStatus();

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">System Health</h1>
            <p className="text-muted-foreground">
              Monitor system status and service health in real-time.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {lastUpdated && (
              <span className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={fetchData} disabled={loading} variant="outline">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Overall Status */}
        <Card className={`border-2 ${
          overallStatus === "healthy" ? "border-green-200 bg-green-50" :
          overallStatus === "warning" ? "border-yellow-200 bg-yellow-50" :
          overallStatus === "error" ? "border-red-200 bg-red-50" :
          "border-gray-200"
        }`}>
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 rounded-full bg-white">
                {getStatusIcon(overallStatus)}
              </div>
              <div>
                <h2 className="text-2xl font-bold">System Status</h2>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge className={getStatusColor(overallStatus)}>
                    {overallStatus.charAt(0).toUpperCase() + overallStatus.slice(1)}
                  </Badge>
                  {data?.timestamp && (
                    <span className="text-sm text-muted-foreground">
                      as of {new Date(data.timestamp).toLocaleString()}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && !data && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {/* Service Status */}
        {data && (
          <>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Server className="h-5 w-5" />
                  <span>Service Status</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Main System Status */}
                  <Card className="border-l-4 border-l-blue-500">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Shield className="h-6 w-6 text-blue-600" />
                          <div>
                            <h3 className="font-semibold">System</h3>
                            <p className="text-sm text-muted-foreground">Main Application</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(data.status)}
                          <Badge className={getStatusColor(data.status)}>
                            {data.status}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Database Status */}
                  {data.services?.database && (
                    <Card className="border-l-4 border-l-green-500">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Database className="h-6 w-6 text-green-600" />
                            <div>
                              <h3 className="font-semibold">Database</h3>
                              <p className="text-sm text-muted-foreground">Data Storage</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(data.services.database)}
                            <Badge className={getStatusColor(data.services.database)}>
                              {data.services.database}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Analytics Status */}
                  {data.services?.analytics && (
                    <Card className="border-l-4 border-l-purple-500">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Activity className="h-6 w-6 text-purple-600" />
                            <div>
                              <h3 className="font-semibold">Analytics</h3>
                              <p className="text-sm text-muted-foreground">Data Processing</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(data.services.analytics)}
                            <Badge className={getStatusColor(data.services.analytics)}>
                              {data.services.analytics}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* System Information */}
            <Card>
              <CardHeader>
                <CardTitle>System Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Health Check Details</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Status:</span>
                        <Badge className={getStatusColor(data.status)}>
                          {data.status}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Timestamp:</span>
                        <span className="font-medium">
                          {data.timestamp ? new Date(data.timestamp).toLocaleString() : "N/A"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Response Time:</span>
                        <span className="font-medium">
                          {lastUpdated ? `${Date.now() - lastUpdated.getTime()}ms` : "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Service Dependencies</h3>
                    <div className="space-y-2">
                      {data.services && Object.entries(data.services).map(([service, status]: [string, any]) => (
                        <div key={service} className="flex justify-between items-center">
                          <span className="text-muted-foreground capitalize">{service}:</span>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(status)}
                            <Badge className={getStatusColor(status)}>
                              {status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Auto-refresh Notice */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Activity className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-muted-foreground">
                    This page automatically refreshes every 30 seconds to provide real-time status updates.
                  </span>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AdminLayout>
  );
}
