import { ThemeProvider } from "@/components/theme-provider";
import type { Metadata } from "next";
import "./globals.css";
import { <PERSON><PERSON>rov<PERSON> } from "@clerk/nextjs";
import { SessionProvider } from "@/components/providers/SessionProvider";
import { ClerkSessionProvider } from "@/components/providers/ClerkSessionProvider";
import { QueryProvider } from "@/components/providers/QueryProvider";
import { Toaster } from "@/components/ui/sonner";
import { BackgroundProvider } from "@/contexts/background-context";
import { outfit } from "@/lib/fonts";
import { PHProvider } from "@/components/analytics/PostHogProvider";
import { generateSEOMetadata } from "@/components/seo/SEOHead";
import { StructuredData, structuredDataSchemas } from "@/components/seo/StructuredData";

export const metadata: Metadata = generateSEOMetadata({
  title: "Siift - Modern Project Management",
  description: "A modern project management platform built with Next.js and NestJS. Streamline your workflow with powerful tools and intuitive design.",
  keywords: ["project management", "productivity", "collaboration", "workflow", "task management", "team collaboration"],
  url: "/",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Organization structured data
  const organizationData = structuredDataSchemas.organization({
    name: "Siift",
    url: "https://siift.app",
    logo: "https://siift.app/images/logo.png",
    description: "A modern project management platform built with Next.js and NestJS",
    socialMedia: [
      "https://twitter.com/siiftapp",
      "https://linkedin.com/company/siift"
    ],
  });

  // Website structured data
  const websiteData = structuredDataSchemas.website({
    name: "Siift",
    url: "https://siift.app",
    description: "A modern project management platform built with Next.js and NestJS",
    searchUrl: "https://siift.app/search?q={search_term_string}",
  });

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Structured Data */}
        <StructuredData data={[organizationData, websiteData]} />
      </head>
      <body
        className={`${outfit.variable} antialiased font-outfit`}
        suppressHydrationWarning
      >
        <ClerkProvider>
          <QueryProvider>
            <PHProvider>
              <ThemeProvider
                attribute="class"
                defaultTheme="system"
                enableSystem
                disableTransitionOnChange
              >
                <BackgroundProvider>
                  <SessionProvider>
                    <ClerkSessionProvider>
                      {children}
                      <Toaster />
                    </ClerkSessionProvider>
                  </SessionProvider>
                </BackgroundProvider>
              </ThemeProvider>
            </PHProvider>
          </QueryProvider>
        </ClerkProvider>
      </body>
    </html>
  );
}
