import { NextRequest, NextResponse } from 'next/server';

// Mock project data (same as in the main route)
const mockProjects = [
  {
    id: "1",
    name: "Website Redesign",
    description: "Complete redesign of company website with modern UI",
    status: "active",
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20'),
    userId: "user1"
  },
  {
    id: "2", 
    name: "Mobile App",
    description: "iOS and Android app development for the platform",
    status: "active",
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18'),
    userId: "user1"
  },
  {
    id: "3",
    name: "Marketing Campaign",
    description: "Q1 marketing campaign planning and execution",
    status: "completed",
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-15'),
    userId: "user1"
  },
  {
    id: "4",
    name: "API Documentation",
    description: "Comprehensive API documentation and developer guides",
    status: "active",
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-19'),
    userId: "user1"
  },
  {
    id: "5",
    name: "Database Migration",
    description: "Migrate legacy database to new cloud infrastructure",
    status: "archived",
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-16'),
    userId: "user1"
  }
];

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Find project by ID
    const project = mockProjects.find(p => p.id === id);
    
    if (!project) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Project not found' 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: project
    });
  } catch (error) {
    console.error('Error fetching project:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch project' 
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    // Find project by ID
    const projectIndex = mockProjects.findIndex(p => p.id === id);
    
    if (projectIndex === -1) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Project not found' 
        },
        { status: 404 }
      );
    }

    // Update project
    const updatedProject = {
      ...mockProjects[projectIndex],
      ...body,
      updatedAt: new Date()
    };
    
    mockProjects[projectIndex] = updatedProject;

    return NextResponse.json({
      success: true,
      data: updatedProject
    });
  } catch (error) {
    console.error('Error updating project:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update project' 
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Find project by ID
    const projectIndex = mockProjects.findIndex(p => p.id === id);
    
    if (projectIndex === -1) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Project not found' 
        },
        { status: 404 }
      );
    }

    // Remove project
    mockProjects.splice(projectIndex, 1);

    return NextResponse.json({
      success: true,
      message: 'Project deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting project:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to delete project' 
      },
      { status: 500 }
    );
  }
}
