import { NextRequest, NextResponse } from 'next/server';

// Mock project data
const mockProjects = [
  {
    id: "1",
    name: "Website Redesign",
    description: "Complete redesign of company website with modern UI",
    status: "active",
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20'),
    userId: "user1"
  },
  {
    id: "2", 
    name: "Mobile App",
    description: "iOS and Android app development for the platform",
    status: "active",
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18'),
    userId: "user1"
  },
  {
    id: "3",
    name: "Marketing Campaign",
    description: "Q1 marketing campaign planning and execution",
    status: "completed",
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-15'),
    userId: "user1"
  },
  {
    id: "4",
    name: "API Documentation",
    description: "Comprehensive API documentation and developer guides",
    status: "active",
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-19'),
    userId: "user1"
  },
  {
    id: "5",
    name: "Database Migration",
    description: "Migrate legacy database to new cloud infrastructure",
    status: "archived",
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-16'),
    userId: "user1"
  }
];

export async function GET(request: NextRequest) {
  try {
    // In a real app, you would:
    // 1. Verify authentication
    // 2. Get user ID from token
    // 3. Fetch projects from database
    
    // For now, return mock data
    return NextResponse.json({
      success: true,
      data: mockProjects
    });
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch projects' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description } = body;

    // Validate required fields
    if (!name || !description) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Name and description are required' 
        },
        { status: 400 }
      );
    }

    // Create new project (mock)
    const newProject = {
      id: `project_${Date.now()}`,
      name,
      description,
      status: "active" as const,
      createdAt: new Date(),
      updatedAt: new Date(),
      userId: "user1"
    };

    // In a real app, you would save to database
    mockProjects.push(newProject);

    return NextResponse.json({
      success: true,
      data: newProject
    });
  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create project' 
      },
      { status: 500 }
    );
  }
}
