"use client";

import { useQuery } from '@tanstack/react-query';
import { useClerkAuth } from '@/hooks/useClerkAuth';
import { queryKeys } from '@/lib/queryClient';

export interface User {
  id: string;
  clerkId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: "user" | "admin";
  status: "active" | "inactive";
  avatarUrl?: string;
  bio?: string;
  timezone: string;
  preferences: {
    notifications: boolean;
    theme: "light" | "dark" | "system";
    language: string;
  };
  createdAt: string;
  updatedAt: string;
}

/**
 * Hook to fetch user data by Clerk ID
 * Automatically caches and manages loading/error states
 */
export function useUser() {
  const { userId, getToken, isSignedIn } = useClerkAuth();

  return useQuery({
    queryKey: queryKeys.user(userId),
    queryFn: async (): Promise<User> => {
      if (!userId) {
        throw new Error('User ID not available');
      }

      const token = await getToken();
      const backendUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
      const url = `${backendUrl}/api/users/clerk/${userId}`;

      console.log('Fetching user from backend:', {
        url,
        userId,
        hasToken: !!token
      });

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` }),
        },
      });

      console.log('Backend response:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('User not found in backend');
        }
        throw new Error(`Failed to fetch user: ${response.status} ${response.statusText}`);
      }

      return response.json();
    },
    enabled: !!userId && isSignedIn, // Only run when user is signed in and has ID
    staleTime: 5 * 60 * 1000, // Consider fresh for 5 minutes
    gcTime: 10 * 60 * 1000,   // Keep in cache for 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry if user not found
      if (error?.message?.includes('User not found')) {
        return false;
      }
      return failureCount < 2;
    },
  });
}

/**
 * Hook to get user profile data with additional metadata
 */
export function useUserProfile() {
  const { userId, getToken, isSignedIn } = useClerkAuth();

  return useQuery({
    queryKey: queryKeys.userProfile(userId),
    queryFn: async () => {
      if (!userId) {
        throw new Error('User ID not available');
      }

      const token = await getToken();
      const backendUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";
      
      const response = await fetch(`${backendUrl}/api/users/clerk/${userId}/profile`, {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` }),
        },
      });

      if (!response.ok) {
        // If profile endpoint doesn't exist, fall back to regular user data
        if (response.status === 404) {
          const userResponse = await fetch(`${backendUrl}/api/users/clerk/${userId}`, {
            headers: {
              'Content-Type': 'application/json',
              ...(token && { 'Authorization': `Bearer ${token}` }),
            },
          });
          
          if (userResponse.ok) {
            return userResponse.json();
          }
        }
        throw new Error(`Failed to fetch user profile: ${response.status}`);
      }

      return response.json();
    },
    enabled: !!userId && isSignedIn,
    staleTime: 2 * 60 * 1000, // Profile data might change more frequently
  });
}

/**
 * Hook to check if user exists in backend
 * Useful for determining if user needs to be synced
 */
export function useUserExists() {
  const { userId, getToken, isSignedIn } = useClerkAuth();

  return useQuery({
    queryKey: ['user', 'exists', userId],
    queryFn: async (): Promise<boolean> => {
      if (!userId) return false;

      try {
        const token = await getToken();
        const backendUrl = process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";

        const response = await fetch(`${backendUrl}/api/users/clerk/${userId}`, {
          method: 'GET', // Use GET instead of HEAD
          headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` }),
          },
        });

        console.log('User exists check:', {
          userId,
          status: response.status,
          exists: response.ok
        });

        return response.ok;
      } catch (error) {
        console.error('Error checking if user exists:', error);
        return false;
      }
    },
    enabled: !!userId && isSignedIn,
    staleTime: 30 * 1000, // Check existence frequently
    retry: false, // Don't retry existence checks
  });
}
