# Cursor Rules for Next.js + Zustand Project

## Project Overview
This is a Next.js 14+ application using:
- TypeScript for type safety
- Zustand for state management
- Tailwind CSS for styling
- React Hook Form for form handling
- Zod for schema validation
- Lucide React for icons

## File Structure & Naming Conventions

### Directory Structure
```
src/
├── app/                    # Next.js App Router pages
├── components/             # Reusable UI components
│   ├── ui/                # Base UI components (buttons, inputs, etc.)
│   └── [feature]/         # Feature-specific components
├── lib/                   # Utilities, helpers, and configurations
├── stores/                # Zustand stores
├── types/                 # TypeScript type definitions
├── hooks/                 # Custom React hooks
└── utils/                 # Utility functions
```

### File Naming
- **Components**: PascalCase (e.g., `UserProfile.tsx`, `AuthCard.tsx`)
- **Pages**: kebab-case (e.g., `user-profile.tsx`, `auth-card.tsx`)
- **Stores**: camelCase with `.store.ts` suffix (e.g., `userStore.ts`, `authStore.ts`)
- **Hooks**: camelCase with `use` prefix (e.g., `useAuth.ts`, `useLocalStorage.ts`)
- **Types**: PascalCase with `.types.ts` suffix (e.g., `User.types.ts`, `Auth.types.ts`)
- **Utils**: camelCase (e.g., `formatDate.ts`, `validateEmail.ts`)

## State Management with Zustand

### Store Structure
```typescript
// stores/userStore.ts
import { create } from zustand'
import { devtools, persist } from 'zustand/middleware'

interface UserState {
  user: User | null
  isLoading: boolean
  error: string | null
  
  // Actions
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  logout: () => void
}

export const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set) => ({
        user: null,
        isLoading: false,
        error: null,
        
        setUser: (user) => set({ user, error: null }),
        setLoading: (isLoading) => set({ isLoading }),
        setError: (error) => set({ error }),
        logout: () => set({ user: null, error: null }),
      }),
     [object Object]       name: 'user-storage,        partialize: (state) => ({ user: state.user }),
      }
    )
  )
)
```

### Store Best Practices
- Use TypeScript interfaces for state and actions
- Implement devtools for debugging
- Use persist middleware for client-side storage when needed
- Keep stores focused and single-purpose
- Use immer for complex state updates if needed
- Always handle loading and error states

## Component Guidelines

### Component Structure
```typescript
// components/UserProfile.tsx
'use client'

import[object Object] useState } from 'react
import { useUserStore } from '@/stores/userStore'
import { Button } from@/components/ui/button'
import type [object Object]UserProfileProps } from '@/types/User.types

export function UserProfile({ userId, onUpdate }: UserProfileProps)[object Object] const { user, setUser, isLoading } = useUserStore()
  const [isEditing, setIsEditing] = useState(false)
  
  // Component logic here
  
  return (
    <div className=space-y-4      {/* JSX */}
    </div>
  )
}
```

### Component Best Practices
- Use `'use client'` directive for client components
- Import types from dedicated type files
- Use destructuring for props
- Implement proper error boundaries
- Use React.memo for performance optimization when needed
- Keep components focused and single-purpose

## Form Handling

### React Hook Form + Zod
```typescript
// types/forms.ts
import { z } from 'zod'

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8,Password must be at least8ters'),
  rememberMe: z.boolean().optional(),
})

export type LoginFormData = z.infer<typeof loginSchema>

// components/LoginForm.tsx
import { useForm } from react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { loginSchema, type LoginFormData } from@/types/forms

export function LoginForm() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: ,    password: ,
      rememberMe: false,
    },
  })
  
  const onSubmit = async (data: LoginFormData) =>[object Object]    try[object Object]    // Handle form submission
    } catch (error) [object Object]    setError(root', { message: Login failed })
    }
  }
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Form fields */}
    </form>
  )
}
```

## API Integration

### API Client Structure
```typescript
// lib/api.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || http://localhost:300class ApiClient {
  private async request<T>(
    endpoint: string,
    options: RequestInit = [object Object]  ): Promise<T>[object Object]   const url = `${API_BASE_URL}${endpoint}`
    const config: RequestInit = {
      headers: [object Object]    Content-Type':application/json',
        ...options.headers,
      },
      ...options,
    }
    
    const response = await fetch(url, config)
    
    if (!response.ok)[object Object]   throw new Error(`API Error: ${response.status}`)
    }
    
    return response.json()
  }
  
  // API methods
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return this.request('/auth/login,[object Object]     method: POST',
      body: JSON.stringify(credentials),
    })
  }
}

export const apiClient = new ApiClient()
```

## Styling Guidelines

### Tailwind CSS
- Use Tailwind utility classes for styling
- Create custom components in `components/ui/` for reusable elements
- Use CSS variables for theme colors
- Implement responsive design with Tailwind breakpoints
- Use consistent spacing scale (4 8, 12, 16, 2024, 32, 48, 64

### Component Styling
```typescript
// Good
<div className=flex items-center justify-between p-4g-white rounded-lg shadow-sm border border-gray-200oid
<div className=flex items-center justify-between p-4" style=[object Object][object Object]backgroundColor: 'white, borderRadius:8px }}>
```

## Error Handling

### Global Error Handling
```typescript
// lib/errorHandler.ts
export class AppError extends Error[object Object]  constructor(
    message: string,
    public code: string,
    public statusCode: number = 50
    super(message)
    this.name = AppError'
  }
}

export const handleError = (error: unknown): string => {
  if (error instanceof AppError) {
    return error.message
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  return 'An unexpected error occurred'
}
```

## Performance Best Practices

### Code Splitting
- Use dynamic imports for large components
- Implement route-based code splitting
- Lazy load non-critical components

### Optimization
```typescript
// Use React.memo for expensive components
export const ExpensiveComponent = React.memo(function ExpensiveComponent({ data })[object Object]
  return <div>{/* Component content */}</div>
})

// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data)
}, [data])

// Use useCallback for event handlers passed to child components
const handleClick = useCallback(() => {
  // Handle click
}, [dependencies])
```

## Testing Guidelines

### Test Structure
```typescript
// __tests__/components/UserProfile.test.tsx
import { render, screen, fireEvent } from@testing-library/react'
import { UserProfile } from @/components/UserProfile'

describe('UserProfile', () => {
  it('renders user information correctly', () => {
    render(<UserProfile userId="123 />)
    expect(screen.getByText('User Profile')).toBeInTheDocument()
  })
  
  it(handles edit mode correctly', () => {
    render(<UserProfile userId=123 />)
    fireEvent.click(screen.getByText('Edit'))
    expect(screen.getByText(Save.toBeInTheDocument()
  })
})
```

## Environment Configuration

### Environment Variables
```bash
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:3000pi
NEXT_PUBLIC_APP_NAME=My App
DATABASE_URL=postgresql://...
JWT_SECRET=your-secret-key
```

## Security Best Practices

### Authentication
- Use HTTP-only cookies for JWT tokens
- Implement proper CSRF protection
- Validate all user inputs
- Use environment variables for sensitive data
- Implement rate limiting

### Data Validation
- Always validate data on both client and server
- Use Zod schemas for type safety
- Sanitize user inputs
- Implement proper error messages

## Accessibility (a11y)

### Guidelines
- Use semantic HTML elements
- Implement proper ARIA labels
- Ensure keyboard navigation
- Maintain color contrast ratios
- Provide alt text for images
- Test with screen readers

```typescript
// Good accessibility example
<button
  aria-label="Close modal"
  onClick={handleClose}
  className="p-2 rounded-md hover:bg-gray-100>
  <X className="w-4h-4" />
</button>
```

## Code Quality

### ESLint Configuration
- Use strict TypeScript rules
- Enforce consistent code style
- Prevent common mistakes
- Use import sorting

### Prettier Configuration
- Use consistent formatting
- Set line length to 800cters
- Use single quotes for strings
- Use trailing commas

## Git Workflow

### Commit Messages
- Use conventional commits format
- Be descriptive and concise
- Reference issues when applicable

```
feat: add user authentication system
fix: resolve login form validation issue
docs: update API documentation
style: improve button component styling
refactor: extract common form logic
test: add unit tests for user store
```

### Branch Naming
- `feature/user-authentication`
- `fix/login-validation`
- `hotfix/security-patch`
- `refactor/state-management`

## Documentation

### Code Documentation
- Use JSDoc for complex functions
- Document component props with TypeScript
- Maintain README files
- Document API endpoints

```typescript
/**
 * Authenticates a user with email and password
 * @param credentials - User login credentials
 * @returns Promise resolving to authentication result
 * @throws {AppError} When authentication fails
 */
async function authenticateUser(credentials: LoginCredentials): Promise<AuthResult> {
  // Implementation
}
```

## Deployment

### Build Optimization
- Use Next.js built-in optimizations
- Implement proper caching strategies
- Optimize images and assets
- Use CDN for static assets

### Environment Setup
- Configure production environment variables
- Set up monitoring and logging
- Implement health checks
- Configure error tracking

---

## Quick Reference

### Common Patterns
- **State Management**: Zustand stores in `stores/`
- **Forms**: React Hook Form + Zod validation
- **Styling**: Tailwind CSS utility classes
- **Icons**: Lucide React
- **API**: Custom API client in `lib/api.ts`
- **Types**: Dedicated type files in `types/`

### File Extensions
- `.tsx` for React components
- `.ts` for utilities and types
- `.store.ts` for Zustand stores
- `.types.ts` for type definitions
- `.test.tsx` for component tests

### Import Order1 React and Next.js imports
2. Third-party libraries
3. Internal components
4. Utilities and helpers
5. Types and interfaces

This document should be updated as the project evolves and new patterns emerge. 